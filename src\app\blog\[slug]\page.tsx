import { notFound } from 'next/navigation';
import Layout from '@/components/layout/Layout';
import ArticleDetail from '@/components/blog/ArticleDetail';
import { Article } from '@/types';

// 模拟数据
const mockArticles: Article[] = [
  {
    id: '1',
    title: '如何构建现代化的个人博客网站',
    content: `
# 如何构建现代化的个人博客网站

在这个数字化时代，拥有一个个人博客网站已经成为展示自己技能和想法的重要方式。本文将详细介绍如何使用现代技术栈构建一个功能完整的个人博客网站。

## 技术栈选择

### 前端框架：Next.js
Next.js是一个基于React的全栈框架，提供了许多开箱即用的功能：
- 服务端渲染(SSR)和静态站点生成(SSG)
- 自动代码分割
- 内置CSS支持
- API路由
- 图片优化

### 样式框架：Tailwind CSS
Tailwind CSS是一个实用优先的CSS框架，具有以下优势：
- 高度可定制
- 响应式设计
- 组件友好
- 构建时优化

### 数据库：Supabase
Supabase是一个开源的Firebase替代品，提供：
- PostgreSQL数据库
- 实时订阅
- 用户认证
- 存储服务
- 边缘函数

## 项目结构设计

\`\`\`
project/
├── components/          # 可复用组件
│   ├── layout/         # 布局组件
│   ├── blog/           # 博客相关组件
│   └── ui/             # UI基础组件
├── pages/              # 页面路由
├── lib/                # 工具函数
├── styles/             # 样式文件
└── types/              # TypeScript类型定义
\`\`\`

## 核心功能实现

### 1. 文章管理系统
- 支持Markdown编写
- 富文本编辑器
- 标签和分类管理
- 草稿和发布状态

### 2. 用户认证
- 管理员登录
- 权限控制
- 会话管理

### 3. 响应式设计
- 移动端适配
- 触摸友好的交互
- 性能优化

## 部署和优化

### 部署选择
推荐使用Vercel进行部署，因为它与Next.js完美集成：
- 自动部署
- 全球CDN
- 边缘函数支持
- 免费SSL证书

### 性能优化
- 图片懒加载
- 代码分割
- 缓存策略
- SEO优化

## 总结

构建一个现代化的个人博客网站需要考虑多个方面，从技术选型到用户体验，每个环节都很重要。通过使用Next.js、Tailwind CSS和Supabase这样的现代技术栈，我们可以快速构建出功能强大、性能优秀的博客网站。

希望这篇文章对你有所帮助！如果你有任何问题，欢迎在评论区留言讨论。
    `,
    excerpt: '学习如何使用现代技术栈构建个人博客，包括前端设计、后端API和数据库设计。',
    slug: 'how-to-build-modern-personal-blog',
    featured_image: '/images/blog-1.jpg',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['Next.js', 'React', 'Tailwind CSS', 'Supabase'],
    view_count: 1250,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    published_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'TypeScript最佳实践指南',
    content: `
# TypeScript最佳实践指南

TypeScript已经成为现代JavaScript开发的标准，它为JavaScript添加了静态类型检查，大大提高了代码的可维护性和开发效率。本文将分享一些TypeScript的最佳实践。

## 基础类型使用

### 1. 优先使用接口而不是类型别名
\`\`\`typescript
// 推荐
interface User {
  id: string;
  name: string;
  email: string;
}

// 不推荐（除非需要联合类型）
type User = {
  id: string;
  name: string;
  email: string;
}
\`\`\`

### 2. 使用严格的类型检查
在tsconfig.json中启用严格模式：
\`\`\`json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
\`\`\`

## 高级类型技巧

### 1. 泛型的使用
\`\`\`typescript
function createApiResponse<T>(data: T): ApiResponse<T> {
  return {
    data,
    success: true,
    timestamp: new Date().toISOString()
  };
}
\`\`\`

### 2. 条件类型
\`\`\`typescript
type NonNullable<T> = T extends null | undefined ? never : T;
\`\`\`

## 项目组织

### 1. 类型定义文件
将类型定义集中在types目录中：
\`\`\`
types/
├── api.ts
├── user.ts
└── index.ts
\`\`\`

### 2. 导入导出规范
\`\`\`typescript
// 使用命名导出
export interface User { ... }
export type ApiResponse<T> = { ... }

// 统一导出
export * from './user';
export * from './api';
\`\`\`

## 总结

TypeScript的强大之处在于它的类型系统，合理使用这些最佳实践可以让你的代码更加健壮和易于维护。
    `,
    excerpt: '掌握TypeScript的核心概念和最佳实践，提升代码质量和开发效率。',
    slug: 'typescript-best-practices-guide',
    published: true,
    author_id: '1',
    category_id: '2',
    tags: ['TypeScript', 'JavaScript', '最佳实践'],
    view_count: 890,
    created_at: '2024-01-10T14:30:00Z',
    updated_at: '2024-01-10T14:30:00Z',
    published_at: '2024-01-10T14:30:00Z',
  },
];

interface PageProps {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: PageProps) {
  const { slug } = await params;
  const article = mockArticles.find(a => a.slug === slug);
  
  if (!article) {
    return {
      title: '文章未找到',
    };
  }

  return {
    title: `${article.title} - 王不留行714的个人博客`,
    description: article.excerpt,
  };
}

export default async function ArticlePage({ params }: PageProps) {
  const { slug } = await params;
  const article = mockArticles.find(a => a.slug === slug);

  if (!article) {
    notFound();
  }

  return (
    <Layout>
      <ArticleDetail article={article} />
    </Layout>
  );
}
