import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Article, PaginatedResponse } from '@/types';

// GET /api/articles - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const category = searchParams.get('category');
    const published = searchParams.get('published') !== 'false';
    
    const offset = (page - 1) * limit;

    let query = supabase
      .from('articles')
      .select(`
        *,
        category:categories(*)
      `, { count: 'exact' });

    // 只显示已发布的文章（前台）
    if (published) {
      query = query.eq('published', true);
    }

    // 按分类筛选
    if (category && category !== 'all') {
      query = query.eq('category_id', category);
    }

    // 分页和排序
    query = query
      .order('published_at', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('获取文章失败:', error);
      return NextResponse.json(
        { error: '获取文章失败' },
        { status: 500 }
      );
    }

    const response: PaginatedResponse<Article> = {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/articles - 创建新文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必填字段
    if (!body.title || !body.content || !body.slug) {
      return NextResponse.json(
        { error: '标题、内容和URL别名为必填项' },
        { status: 400 }
      );
    }

    // 检查slug是否已存在
    const { data: existingArticle } = await supabase
      .from('articles')
      .select('id')
      .eq('slug', body.slug)
      .single();

    if (existingArticle) {
      return NextResponse.json(
        { error: 'URL别名已存在' },
        { status: 400 }
      );
    }

    const articleData = {
      title: body.title,
      content: body.content,
      excerpt: body.excerpt || '',
      slug: body.slug,
      featured_image: body.featured_image,
      published: body.published || false,
      author_id: body.author_id,
      category_id: body.category_id,
      tags: body.tags || [],
      published_at: body.published ? new Date().toISOString() : null,
    };

    const { data, error } = await supabase
      .from('articles')
      .insert([articleData])
      .select(`
        *,
        category:categories(*)
      `)
      .single();

    if (error) {
      console.error('创建文章失败:', error);
      return NextResponse.json(
        { error: '创建文章失败' },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
