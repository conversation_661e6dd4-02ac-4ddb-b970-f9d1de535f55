#!/bin/bash

# 部署脚本
# 使用方法: ./scripts/deploy.sh [环境]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-production}

log_info "开始部署到 $ENVIRONMENT 环境..."

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."
    npm ci --only=production
    log_info "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 设置环境变量
    export NODE_ENV=production
    
    # 构建
    npm run build
    
    log_info "项目构建完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 如果有测试脚本，在这里运行
    if npm run test --silent 2>/dev/null; then
        npm run test
    else
        log_warn "未找到测试脚本，跳过测试"
    fi
    
    log_info "测试完成"
}

# 部署到生产环境
deploy_production() {
    log_info "部署到生产环境..."
    
    # 这里可以添加具体的部署逻辑
    # 例如：上传到服务器、重启服务等
    
    case "$DEPLOYMENT_METHOD" in
        "docker")
            deploy_docker
            ;;
        "pm2")
            deploy_pm2
            ;;
        "vercel")
            deploy_vercel
            ;;
        *)
            log_warn "未指定部署方法，使用默认方法"
            deploy_default
            ;;
    esac
    
    log_info "生产环境部署完成"
}

# Docker部署
deploy_docker() {
    log_info "使用Docker部署..."
    
    # 构建Docker镜像
    docker build -t personal-blog:latest .
    
    # 停止旧容器
    docker stop personal-blog || true
    docker rm personal-blog || true
    
    # 启动新容器
    docker run -d \
        --name personal-blog \
        --env-file .env.local \
        -p 3000:3000 \
        personal-blog:latest
    
    log_info "Docker部署完成"
}

# PM2部署
deploy_pm2() {
    log_info "使用PM2部署..."
    
    # 安装PM2（如果未安装）
    if ! command -v pm2 &> /dev/null; then
        npm install -g pm2
    fi
    
    # 启动或重启应用
    pm2 start ecosystem.config.js --env production || pm2 restart ecosystem.config.js --env production
    
    # 保存PM2配置
    pm2 save
    
    log_info "PM2部署完成"
}

# Vercel部署
deploy_vercel() {
    log_info "使用Vercel部署..."
    
    # 安装Vercel CLI（如果未安装）
    if ! command -v vercel &> /dev/null; then
        npm install -g vercel
    fi
    
    # 部署到Vercel
    vercel --prod
    
    log_info "Vercel部署完成"
}

# 默认部署方法
deploy_default() {
    log_info "使用默认部署方法..."
    
    # 启动应用
    npm start
    
    log_info "默认部署完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 5
    
    # 检查服务是否正常
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        log_info "健康检查通过"
    else
        log_error "健康检查失败"
        exit 1
    fi
}

# 清理
cleanup() {
    log_info "清理临时文件..."
    
    # 清理构建缓存
    rm -rf .next/cache
    
    # 清理日志文件
    find . -name "*.log" -type f -delete
    
    log_info "清理完成"
}

# 主函数
main() {
    log_info "开始部署流程..."
    
    # 检查环境变量
    if [ ! -f ".env.local" ]; then
        log_error "未找到 .env.local 文件"
        exit 1
    fi
    
    # 加载环境变量
    source .env.local
    
    # 执行部署步骤
    check_dependencies
    install_dependencies
    run_tests
    build_project
    
    if [ "$ENVIRONMENT" = "production" ]; then
        deploy_production
        health_check
    fi
    
    cleanup
    
    log_info "部署完成！"
    log_info "访问地址: ${NEXT_PUBLIC_SITE_URL:-http://localhost:3000}"
}

# 错误处理
trap 'log_error "部署失败！"; exit 1' ERR

# 执行主函数
main "$@"
