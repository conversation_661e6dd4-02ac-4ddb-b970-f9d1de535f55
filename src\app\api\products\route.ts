import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Product, PaginatedResponse } from '@/types';

// GET /api/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const featured = searchParams.get('featured');
    
    const offset = (page - 1) * limit;

    let query = supabase
      .from('products')
      .select('*', { count: 'exact' });

    // 按状态筛选
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    // 按特色产品筛选
    if (featured === 'true') {
      query = query.eq('featured', true);
    }

    // 分页和排序
    query = query
      .order('featured', { ascending: false })
      .order('launch_date', { ascending: false, nullsFirst: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('获取产品失败:', error);
      return NextResponse.json(
        { error: '获取产品失败' },
        { status: 500 }
      );
    }

    const response: PaginatedResponse<Product> = {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/products - 创建新产品
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必填字段
    if (!body.name || !body.description) {
      return NextResponse.json(
        { error: '产品名称和描述为必填项' },
        { status: 400 }
      );
    }

    // 验证产品状态
    const validStatuses = ['coming_soon', 'available', 'discontinued'];
    if (body.status && !validStatuses.includes(body.status)) {
      return NextResponse.json(
        { error: '无效的产品状态' },
        { status: 400 }
      );
    }

    const productData = {
      name: body.name,
      description: body.description,
      image_url: body.image_url,
      product_url: body.product_url,
      status: body.status || 'coming_soon',
      featured: body.featured || false,
      launch_date: body.launch_date || null,
    };

    const { data, error } = await supabase
      .from('products')
      .insert([productData])
      .select()
      .single();

    if (error) {
      console.error('创建产品失败:', error);
      return NextResponse.json(
        { error: '创建产品失败' },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
