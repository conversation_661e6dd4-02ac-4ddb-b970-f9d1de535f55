{"name": "wblx", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "docker:build": "docker build -t personal-blog .", "docker:run": "docker run -p 3000:3000 --env-file .env.local personal-blog", "db:generate": "echo 'Run database/init.sql in your Supabase console'", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist build"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.6.0", "tailwindcss": "^4", "typescript": "^5"}}