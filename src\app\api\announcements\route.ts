import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { Announcement, PaginatedResponse } from '@/types';

// GET /api/announcements - 获取公告列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const published = searchParams.get('published') !== 'false';
    
    const offset = (page - 1) * limit;

    let query = supabase
      .from('announcements')
      .select('*', { count: 'exact' });

    // 只显示已发布的公告（前台）
    if (published) {
      query = query.eq('published', true);
      
      // 过滤未过期的公告
      query = query.or('expires_at.is.null,expires_at.gt.' + new Date().toISOString());
    }

    // 分页和排序
    query = query
      .order('priority', { ascending: false })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('获取公告失败:', error);
      return NextResponse.json(
        { error: '获取公告失败' },
        { status: 500 }
      );
    }

    const response: PaginatedResponse<Announcement> = {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// POST /api/announcements - 创建新公告
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证必填字段
    if (!body.title || !body.content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      );
    }

    // 验证公告类型
    const validTypes = ['info', 'warning', 'success', 'error'];
    if (body.type && !validTypes.includes(body.type)) {
      return NextResponse.json(
        { error: '无效的公告类型' },
        { status: 400 }
      );
    }

    const announcementData = {
      title: body.title,
      content: body.content,
      type: body.type || 'info',
      published: body.published || false,
      priority: body.priority || 0,
      expires_at: body.expires_at || null,
    };

    const { data, error } = await supabase
      .from('announcements')
      .insert([announcementData])
      .select()
      .single();

    if (error) {
      console.error('创建公告失败:', error);
      return NextResponse.json(
        { error: '创建公告失败' },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
