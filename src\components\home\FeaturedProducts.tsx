'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowTopRightOnSquareIcon, ClockIcon } from '@heroicons/react/24/outline';
import { Product } from '@/types';
import { formatDate } from '@/lib/utils';

// 模拟数据
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'AI写作助手',
    description: '基于人工智能的智能写作工具，帮助您快速生成高质量的文章内容。支持多种写作风格和主题。',
    image_url: '/images/product-1.jpg',
    product_url: 'https://example.com/ai-writer',
    status: 'available',
    featured: true,
    launch_date: '2024-01-15T00:00:00Z',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: '代码片段管理器',
    description: '专为开发者设计的代码片段管理工具，支持多种编程语言，云端同步，团队协作。',
    image_url: '/images/product-2.jpg',
    status: 'coming_soon',
    featured: true,
    launch_date: '2024-02-20T00:00:00Z',
    created_at: '2024-01-12T14:30:00Z',
    updated_at: '2024-01-12T14:30:00Z',
  },
  {
    id: '3',
    name: '个人知识库',
    description: '构建您的个人知识管理系统，支持笔记整理、标签分类、全文搜索和知识图谱可视化。',
    image_url: '/images/product-3.jpg',
    status: 'coming_soon',
    featured: true,
    launch_date: '2024-03-10T00:00:00Z',
    created_at: '2024-01-08T09:15:00Z',
    updated_at: '2024-01-08T09:15:00Z',
  },
];

const getStatusBadge = (status: Product['status']) => {
  switch (status) {
    case 'available':
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          已发布
        </span>
      );
    case 'coming_soon':
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <ClockIcon className="h-3 w-3 mr-1" />
          即将发布
        </span>
      );
    case 'discontinued':
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          已停用
        </span>
      );
    default:
      return null;
  }
};

export default function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟API调用
    const fetchProducts = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 800));
        setProducts(mockProducts);
      } catch (error) {
        console.error('获取产品失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) {
    return (
      <section className="py-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">特色产品</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4 w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded mb-4"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-6 bg-gray-300 rounded w-16"></div>
                    <div className="h-8 bg-gray-300 rounded w-20"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">特色产品</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          探索我们精心打造的产品和工具
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {products.map((product) => (
          <div
            key={product.id}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            {product.image_url && (
              <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="absolute top-4 right-4">
                  {getStatusBadge(product.status)}
                </div>
              </div>
            )}
            
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {product.name}
              </h3>
              <p className="text-gray-600 mb-4 line-clamp-3">
                {product.description}
              </p>
              
              {product.launch_date && (
                <div className="text-sm text-gray-500 mb-4">
                  {product.status === 'available' ? '发布时间' : '预计发布'}: {formatDate(product.launch_date)}
                </div>
              )}
              
              <div className="flex justify-between items-center">
                {getStatusBadge(product.status)}
                
                {product.status === 'available' && product.product_url ? (
                  <a
                    href={product.product_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  >
                    访问产品
                    <ArrowTopRightOnSquareIcon className="ml-2 h-4 w-4" />
                  </a>
                ) : (
                  <button
                    disabled
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed"
                  >
                    敬请期待
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center">
        <Link
          href="/products"
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          查看所有产品
        </Link>
      </div>
    </section>
  );
}
