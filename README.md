# 王不留行714的个人博客

一个现代化的个人博客网站，使用 Next.js、Tailwind CSS 和 Supabase 构建。

## 功能特性

### 前台功能
- 🏠 **首页展示** - 最新文章、公告和产品预告
- 📝 **文章系统** - 文章列表、详情页面、分类筛选
- 🏷️ **标签系统** - 文章标签分类和筛选
- 📢 **公告系统** - 重要通知和公告展示
- 🚀 **产品展示** - 产品介绍和状态管理
- 📱 **响应式设计** - 完美适配移动端和桌面端
- 🔍 **SEO优化** - 搜索引擎友好的页面结构

### 后台管理
- 🔐 **管理员登录** - 安全的身份验证系统
- 📊 **仪表板** - 网站数据统计和概览
- ✍️ **文章管理** - 创建、编辑、发布文章
- 📣 **公告管理** - 发布和管理网站公告
- 🎯 **产品管理** - 添加和管理产品信息
- 👥 **用户管理** - 用户权限和角色管理

## 技术栈

- **前端框架**: Next.js 15 (React 19)
- **样式框架**: Tailwind CSS
- **数据库**: Supabase (PostgreSQL)
- **认证系统**: Supabase Auth
- **状态管理**: Zustand
- **图标库**: Heroicons
- **类型检查**: TypeScript
- **代码规范**: ESLint

## 快速开始

### 环境要求

- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- Supabase 账户

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd wblx
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.local.example .env.local
```

编辑 `.env.local` 文件，填入你的 Supabase 配置：
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME=王不留行714的个人博客
ADMIN_EMAIL=<EMAIL>
```

4. **初始化数据库**

在 Supabase 控制台中执行 `database/init.sql` 脚本来创建数据库表和初始数据。

5. **启动开发服务器**
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看网站。

## 项目结构

```
wblx/
├── src/
│   ├── app/                    # Next.js App Router 页面
│   │   ├── admin/             # 管理后台页面
│   │   ├── blog/              # 博客相关页面
│   │   ├── products/          # 产品页面
│   │   └── about/             # 关于页面
│   ├── components/            # React 组件
│   │   ├── admin/             # 管理后台组件
│   │   ├── blog/              # 博客相关组件
│   │   ├── home/              # 首页组件
│   │   ├── layout/            # 布局组件
│   │   └── products/          # 产品组件
│   ├── lib/                   # 工具函数和配置
│   ├── store/                 # 状态管理
│   └── types/                 # TypeScript 类型定义
├── database/                  # 数据库脚本
├── public/                    # 静态资源
└── docs/                      # 项目文档
```

## 使用指南

### 管理后台使用

1. **登录管理后台**
   - 访问 `/admin/login`
   - 使用管理员账户登录（默认：<EMAIL> / password123）

2. **文章管理**
   - 在管理后台点击"文章管理"
   - 可以创建、编辑、发布文章
   - 支持 Markdown 格式和富文本编辑

3. **公告管理**
   - 发布重要通知和公告
   - 支持不同类型的公告（信息、警告、成功、错误）
   - 可设置公告优先级和过期时间

4. **产品管理**
   - 添加和管理产品信息
   - 设置产品状态（即将发布、已发布、已停用）
   - 管理特色产品展示

### 内容创作

1. **文章写作**
   - 支持 Markdown 语法
   - 自动生成文章摘要
   - 标签和分类管理
   - SEO 友好的 URL

2. **图片管理**
   - 支持图片上传和管理
   - 自动图片优化
   - 响应式图片显示

## 部署指南

### Vercel 部署（推荐）

1. **连接 GitHub**
   - 将项目推送到 GitHub
   - 在 Vercel 中导入项目

2. **配置环境变量**
   - 在 Vercel 项目设置中添加环境变量
   - 确保所有必要的环境变量都已配置

3. **部署**
   - Vercel 会自动构建和部署项目
   - 每次推送代码都会自动重新部署

### 其他部署选项

- **Netlify**: 支持静态站点生成
- **Docker**: 提供 Dockerfile 进行容器化部署
- **传统服务器**: 支持 Node.js 环境的服务器

## 开发指南

### 添加新功能

1. **创建组件**
```bash
# 在相应目录下创建组件文件
src/components/feature/NewComponent.tsx
```

2. **添加路由**
```bash
# 在 app 目录下创建页面
src/app/new-page/page.tsx
```

3. **更新类型定义**
```bash
# 在 types 目录下添加类型
src/types/index.ts
```

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 组件使用 PascalCase 命名
- 文件使用 kebab-case 命名

## 常见问题

### Q: 如何修改网站标题和描述？
A: 在 `src/app/layout.tsx` 中修改 metadata 配置。

### Q: 如何添加新的文章分类？
A: 在管理后台的分类管理中添加，或直接在数据库中插入。

### Q: 如何自定义主题颜色？
A: 修改 `tailwind.config.js` 中的颜色配置。

### Q: 如何备份数据？
A: 使用 Supabase 的备份功能或导出数据库数据。

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 作者：王不留行714
- 邮箱：<EMAIL>
- 博客：[https://your-blog-url.com](https://your-blog-url.com)

## 更新日志

### v1.0.0 (2024-01-20)
- 🎉 初始版本发布
- ✨ 完整的博客功能
- 🔧 管理后台系统
- 📱 响应式设计
- 🔐 用户认证系统
