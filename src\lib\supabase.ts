import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 客户端Supabase实例
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 浏览器端Supabase客户端
export function createSupabaseBrowserClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey);
}

// 数据库表名常量
export const TABLES = {
  USERS: 'users',
  ARTICLES: 'articles',
  CATEGORIES: 'categories',
  ANNOUNCEMENTS: 'announcements',
  PRODUCTS: 'products',
} as const;
