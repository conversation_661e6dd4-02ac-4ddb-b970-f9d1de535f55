-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    avatar_url TEXT,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建分类表
CREATE TABLE IF NOT EXISTS categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7), -- 十六进制颜色代码
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建文章表
CREATE TABLE IF NOT EXISTS articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    slug VARCHAR(500) UNIQUE NOT NULL,
    featured_image TEXT,
    published BOOLEAN DEFAULT FALSE,
    author_id UUID REFERENCES users(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    tags TEXT[], -- PostgreSQL数组类型
    view_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);

-- 创建公告表
CREATE TABLE IF NOT EXISTS announcements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('info', 'warning', 'success', 'error')),
    published BOOLEAN DEFAULT FALSE,
    priority INTEGER DEFAULT 0,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    product_url TEXT,
    status VARCHAR(20) DEFAULT 'coming_soon' CHECK (status IN ('coming_soon', 'available', 'discontinued')),
    featured BOOLEAN DEFAULT FALSE,
    launch_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_articles_published ON articles(published);
CREATE INDEX IF NOT EXISTS idx_articles_author_id ON articles(author_id);
CREATE INDEX IF NOT EXISTS idx_articles_category_id ON articles(category_id);
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles(published_at);
CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles(slug);
CREATE INDEX IF NOT EXISTS idx_announcements_published ON announcements(published);
CREATE INDEX IF NOT EXISTS idx_announcements_priority ON announcements(priority);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_featured ON products(featured);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_articles_updated_at BEFORE UPDATE ON articles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_announcements_updated_at BEFORE UPDATE ON announcements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入默认管理员用户
INSERT INTO users (email, name, role) VALUES 
('<EMAIL>', '管理员', 'admin')
ON CONFLICT (email) DO NOTHING;

-- 插入默认分类
INSERT INTO categories (name, slug, description, color) VALUES 
('前端开发', 'frontend', '前端技术相关文章', '#3B82F6'),
('后端开发', 'backend', '后端技术相关文章', '#10B981'),
('TypeScript', 'typescript', 'TypeScript相关文章', '#F59E0B'),
('生活感悟', 'life', '生活感悟和思考', '#EF4444'),
('产品设计', 'design', '产品设计相关文章', '#8B5CF6')
ON CONFLICT (slug) DO NOTHING;

-- 插入示例文章
INSERT INTO articles (title, content, excerpt, slug, published, author_id, category_id, tags, view_count, published_at) 
SELECT 
    '如何构建现代化的个人博客网站',
    '在这篇文章中，我将分享如何使用Next.js、Tailwind CSS和Supabase构建一个功能完整的个人博客网站...',
    '学习如何使用现代技术栈构建个人博客，包括前端设计、后端API和数据库设计。',
    'how-to-build-modern-personal-blog',
    true,
    u.id,
    c.id,
    ARRAY['Next.js', 'React', 'Tailwind CSS', 'Supabase'],
    1250,
    NOW() - INTERVAL '5 days'
FROM users u, categories c 
WHERE u.email = '<EMAIL>' AND c.slug = 'frontend'
ON CONFLICT (slug) DO NOTHING;

-- 插入示例公告
INSERT INTO announcements (title, content, type, published, priority) VALUES 
('网站正式上线！', '欢迎来到王不留行714的个人博客！这里将分享技术文章、生活感悟和最新产品信息。', 'success', true, 1),
('新产品即将发布', '我们正在开发一款全新的工具产品，敬请期待！预计将在下个月正式发布。', 'info', true, 2)
ON CONFLICT DO NOTHING;

-- 插入示例产品
INSERT INTO products (name, description, status, featured, launch_date) VALUES 
('AI写作助手', '基于人工智能的智能写作工具，帮助您快速生成高质量的文章内容。', 'available', true, NOW() - INTERVAL '10 days'),
('代码片段管理器', '专为开发者设计的代码片段管理工具，支持多种编程语言，云端同步，团队协作。', 'coming_soon', true, NOW() + INTERVAL '30 days'),
('个人知识库', '构建您的个人知识管理系统，支持笔记整理、标签分类、全文搜索和知识图谱可视化。', 'coming_soon', true, NOW() + INTERVAL '60 days')
ON CONFLICT DO NOTHING;

-- 启用行级安全策略 (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE announcements ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略

-- 用户表策略
CREATE POLICY "用户可以查看所有用户" ON users FOR SELECT USING (true);
CREATE POLICY "管理员可以管理用户" ON users FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- 分类表策略
CREATE POLICY "所有人可以查看分类" ON categories FOR SELECT USING (true);
CREATE POLICY "管理员可以管理分类" ON categories FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- 文章表策略
CREATE POLICY "所有人可以查看已发布文章" ON articles FOR SELECT USING (published = true);
CREATE POLICY "管理员可以查看所有文章" ON articles FOR SELECT USING (auth.jwt() ->> 'role' = 'admin');
CREATE POLICY "管理员可以管理文章" ON articles FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- 公告表策略
CREATE POLICY "所有人可以查看已发布公告" ON announcements FOR SELECT USING (published = true);
CREATE POLICY "管理员可以管理公告" ON announcements FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- 产品表策略
CREATE POLICY "所有人可以查看产品" ON products FOR SELECT USING (true);
CREATE POLICY "管理员可以管理产品" ON products FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
