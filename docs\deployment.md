# 部署指南

本文档详细介绍如何部署王不留行714的个人博客网站到不同的平台。

## 🚀 宝塔面板部署（推荐用于VPS）

### 前置要求

1. **服务器配置**
   - Ubuntu 18.04+ 或 CentOS 7+
   - 至少 1GB RAM，2GB 推荐
   - 至少 10GB 可用磁盘空间
   - 已安装宝塔面板 7.0+

2. **宝塔面板环境**
   - Node.js 18.0+（通过宝塔软件商店安装）
   - PM2 管理器（通过宝塔软件商店安装）
   - Nginx（通过宝塔面板安装）
   - SSL证书（Let's Encrypt 免费证书）

### 快速部署步骤

1. **创建网站**
   - 在宝塔面板中添加站点：`www.wblx.xyz`
   - 选择 PHP 版本（实际不会用到，但需要选择）
   - 创建数据库（可选，我们使用 Supabase）

2. **上传项目文件**
   ```bash
   # 方法1：直接上传压缩包到 /www/wwwroot/www.wblx.xyz/
   # 方法2：使用Git克隆（推荐）
   cd /www/wwwroot/www.wblx.xyz/
   git clone <your-repo-url> .
   ```

3. **运行一键部署脚本**
   ```bash
   chmod +x deployment/deploy-bt.sh
   ./deployment/deploy-bt.sh
   ```

4. **配置域名和SSL**
   - 在宝塔面板中绑定域名 `www.wblx.xyz`
   - 申请并安装 SSL 证书
   - 强制 HTTPS 重定向

### 详细配置说明

#### 1. Supabase 设置

1. **创建 Supabase 项目**
   - 访问 [Supabase](https://supabase.com)
   - 创建新项目
   - 记录项目 URL 和 API 密钥

2. **初始化数据库**
   - 在 Supabase 控制台的 SQL 编辑器中
   - 执行 `database/init.sql` 脚本
   - 确认所有表和数据都已创建

3. **配置认证**
   - 在 Authentication 设置中启用邮箱登录
   - 配置邮件模板（可选）
   - 设置重定向 URL：`https://www.wblx.xyz/auth/callback`

#### 2. 环境变量配置

在项目根目录创建 `.env.local` 文件：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 网站配置
NEXT_PUBLIC_SITE_URL=https://www.wblx.xyz
NEXT_PUBLIC_SITE_NAME=王不留行714的个人博客

# 管理员配置
ADMIN_EMAIL=<EMAIL>
```

#### 3. Nginx 配置

将 `deployment/nginx-bt.conf` 中的配置添加到宝塔面板的网站配置中。

## 其他部署选项

### 选项 1: Vercel 部署（推荐）

Vercel 是 Next.js 的官方部署平台，提供最佳的性能和开发体验。

#### 步骤：

1. **推送代码到 GitHub**
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. **连接 Vercel**
   - 访问 [Vercel](https://vercel.com)
   - 使用 GitHub 账户登录
   - 导入你的项目仓库

3. **配置环境变量**
   - 在项目设置中添加所有环境变量
   - 确保生产环境的 URL 正确

4. **部署**
   - Vercel 会自动构建和部署
   - 每次推送代码都会触发自动部署

#### 优势：
- 自动 HTTPS
- 全球 CDN
- 自动部署
- 边缘函数支持
- 免费额度充足

### 选项 2: Netlify 部署

Netlify 也是一个优秀的静态站点托管平台。

#### 步骤：

1. **构建设置**
```bash
# Build command
npm run build

# Publish directory
out
```

2. **配置 netlify.toml**
```toml
[build]
  command = "npm run build"
  publish = "out"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

3. **环境变量**
   - 在 Netlify 控制台中配置环境变量

### 选项 3: Docker 部署

适合需要更多控制的部署场景。

#### Dockerfile：

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### 构建和运行：

```bash
# 构建镜像
docker build -t personal-blog .

# 运行容器
docker run -p 3000:3000 --env-file .env.local personal-blog
```

## 域名配置

### 1. DNS 设置

如果使用自定义域名：

```
# A 记录
@ -> Vercel IP 地址

# CNAME 记录
www -> your-project.vercel.app
```

### 2. SSL 证书

- Vercel 和 Netlify 自动提供 SSL 证书
- 自托管需要配置 Let's Encrypt 或其他证书

## 性能优化

### 1. 图片优化

```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
}
```

### 2. 缓存策略

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, stale-while-revalidate=86400',
          },
        ],
      },
    ]
  },
}
```

### 3. 静态生成

```javascript
// 在页面组件中
export async function generateStaticParams() {
  // 预生成静态页面
}
```

## 监控和维护

### 1. 错误监控

推荐集成 Sentry 进行错误监控：

```bash
npm install @sentry/nextjs
```

### 2. 性能监控

- 使用 Vercel Analytics
- Google Analytics
- Web Vitals 监控

### 3. 备份策略

- 定期备份 Supabase 数据库
- 代码版本控制
- 环境变量备份

## 故障排除

### 常见问题：

1. **构建失败**
   - 检查环境变量是否正确
   - 确认依赖版本兼容性

2. **数据库连接失败**
   - 验证 Supabase URL 和密钥
   - 检查网络连接

3. **认证问题**
   - 确认重定向 URL 配置
   - 检查 RLS 策略

### 调试命令：

```bash
# 本地构建测试
npm run build
npm start

# 检查环境变量
npm run env

# 数据库连接测试
npm run db:test
```

## 更新部署

### 自动部署：

1. 推送代码到主分支
2. 部署平台自动构建
3. 检查部署状态

### 手动部署：

```bash
# 构建项目
npm run build

# 上传到服务器
rsync -avz .next/ user@server:/path/to/app/

# 重启服务
pm2 restart app
```

## 安全考虑

1. **环境变量安全**
   - 不要在客户端暴露敏感信息
   - 使用 NEXT_PUBLIC_ 前缀的变量会暴露给客户端

2. **数据库安全**
   - 启用 RLS（行级安全）
   - 定期更新密钥
   - 监控异常访问

3. **HTTPS**
   - 强制使用 HTTPS
   - 配置安全头部

## 成本优化

1. **Vercel 免费额度**
   - 100GB 带宽/月
   - 无限静态请求
   - 100 个 Serverless 函数调用/天

2. **Supabase 免费额度**
   - 500MB 数据库存储
   - 50,000 月活用户
   - 2GB 文件存储

3. **优化建议**
   - 使用静态生成减少服务器负载
   - 优化图片大小
   - 启用缓存策略

通过以上配置，你的个人博客网站就可以成功部署并稳定运行了！
