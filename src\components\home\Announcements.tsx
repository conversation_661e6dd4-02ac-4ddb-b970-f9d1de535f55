'use client';

import { useState, useEffect } from 'react';
import { 
  InformationCircleIcon, 
  ExclamationTriangleIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  XMarkIcon 
} from '@heroicons/react/24/outline';
import { Announcement } from '@/types';
import { formatDate } from '@/lib/utils';
import { cn } from '@/lib/utils';

// 模拟数据
const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: '网站正式上线！',
    content: '欢迎来到王不留行714的个人博客！这里将分享技术文章、生活感悟和最新产品信息。',
    type: 'success',
    published: true,
    priority: 1,
    created_at: '2024-01-20T10:00:00Z',
    updated_at: '2024-01-20T10:00:00Z',
  },
  {
    id: '2',
    title: '新产品即将发布',
    content: '我们正在开发一款全新的工具产品，敬请期待！预计将在下个月正式发布。',
    type: 'info',
    published: true,
    priority: 2,
    created_at: '2024-01-18T15:30:00Z',
    updated_at: '2024-01-18T15:30:00Z',
  },
];

const getAnnouncementIcon = (type: Announcement['type']) => {
  switch (type) {
    case 'info':
      return InformationCircleIcon;
    case 'warning':
      return ExclamationTriangleIcon;
    case 'success':
      return CheckCircleIcon;
    case 'error':
      return XCircleIcon;
    default:
      return InformationCircleIcon;
  }
};

const getAnnouncementStyles = (type: Announcement['type']) => {
  switch (type) {
    case 'info':
      return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'warning':
      return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'success':
      return 'bg-green-50 border-green-200 text-green-800';
    case 'error':
      return 'bg-red-50 border-red-200 text-red-800';
    default:
      return 'bg-blue-50 border-blue-200 text-blue-800';
  }
};

export default function Announcements() {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟API调用
    const fetchAnnouncements = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setAnnouncements(mockAnnouncements);
      } catch (error) {
        console.error('获取公告失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncements();

    // 从localStorage恢复已关闭的公告
    const dismissed = localStorage.getItem('dismissedAnnouncements');
    if (dismissed) {
      setDismissedIds(new Set(JSON.parse(dismissed)));
    }
  }, []);

  const dismissAnnouncement = (id: string) => {
    const newDismissedIds = new Set(dismissedIds);
    newDismissedIds.add(id);
    setDismissedIds(newDismissedIds);
    
    // 保存到localStorage
    localStorage.setItem('dismissedAnnouncements', JSON.stringify([...newDismissedIds]));
  };

  const visibleAnnouncements = announcements.filter(
    announcement => !dismissedIds.has(announcement.id)
  );

  if (loading || visibleAnnouncements.length === 0) {
    return null;
  }

  return (
    <section className="py-8">
      <div className="space-y-4">
        {visibleAnnouncements.map((announcement) => {
          const Icon = getAnnouncementIcon(announcement.type);
          const styles = getAnnouncementStyles(announcement.type);
          
          return (
            <div
              key={announcement.id}
              className={cn(
                'rounded-lg border p-4 relative',
                styles
              )}
            >
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <Icon className="h-5 w-5" aria-hidden="true" />
                </div>
                <div className="ml-3 flex-1">
                  <h3 className="text-sm font-medium">
                    {announcement.title}
                  </h3>
                  <div className="mt-2 text-sm">
                    <p>{announcement.content}</p>
                  </div>
                  <div className="mt-2 text-xs opacity-75">
                    {formatDate(announcement.created_at)}
                  </div>
                </div>
                <div className="ml-auto flex-shrink-0">
                  <button
                    type="button"
                    className="inline-flex rounded-md p-1.5 hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-current"
                    onClick={() => dismissAnnouncement(announcement.id)}
                  >
                    <span className="sr-only">关闭</span>
                    <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </section>
  );
}
