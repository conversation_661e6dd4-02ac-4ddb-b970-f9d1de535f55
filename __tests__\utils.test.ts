import { 
  formatDate, 
  formatRelativeTime, 
  generateExcerpt, 
  generateSlug, 
  isValidEmail,
  truncateText,
  getFileExtension,
  isValidImageType,
  formatFileSize
} from '@/lib/utils';

// Mock date-fns functions for consistent testing
jest.mock('date-fns', () => ({
  format: jest.fn(() => '2024-01-15'),
  formatDistanceToNow: jest.fn(() => '2天前'),
}));

describe('Utils Functions', () => {
  describe('formatDate', () => {
    it('should format date correctly', () => {
      const result = formatDate('2024-01-15T10:00:00Z');
      expect(result).toBe('2024-01-15');
    });
  });

  describe('formatRelativeTime', () => {
    it('should format relative time correctly', () => {
      const result = formatRelativeTime('2024-01-13T10:00:00Z');
      expect(result).toBe('2天前');
    });
  });

  describe('generateExcerpt', () => {
    it('should generate excerpt from plain text', () => {
      const content = 'This is a long text that should be truncated to create an excerpt.';
      const result = generateExcerpt(content, 20);
      expect(result).toBe('This is a long text...');
    });

    it('should remove HTML tags', () => {
      const content = '<p>This is <strong>HTML</strong> content</p>';
      const result = generateExcerpt(content, 50);
      expect(result).toBe('This is HTML content');
    });

    it('should return original text if shorter than maxLength', () => {
      const content = 'Short text';
      const result = generateExcerpt(content, 50);
      expect(result).toBe('Short text');
    });
  });

  describe('generateSlug', () => {
    it('should generate URL-friendly slug', () => {
      const title = 'How to Build a Modern Blog Website';
      const result = generateSlug(title);
      expect(result).toBe('how-to-build-a-modern-blog-website');
    });

    it('should handle special characters', () => {
      const title = 'TypeScript 最佳实践指南！';
      const result = generateSlug(title);
      expect(result).toBe('typescript');
    });

    it('should handle multiple spaces and hyphens', () => {
      const title = 'Multiple   Spaces---And___Underscores';
      const result = generateSlug(title);
      expect(result).toBe('multiple-spaces-and-underscores');
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('truncateText', () => {
    it('should truncate long text', () => {
      const text = 'This is a very long text that needs to be truncated';
      const result = truncateText(text, 20);
      expect(result).toBe('This is a very long...');
    });

    it('should return original text if shorter', () => {
      const text = 'Short text';
      const result = truncateText(text, 20);
      expect(result).toBe('Short text');
    });
  });

  describe('getFileExtension', () => {
    it('should extract file extension', () => {
      expect(getFileExtension('image.jpg')).toBe('jpg');
      expect(getFileExtension('document.pdf')).toBe('pdf');
      expect(getFileExtension('archive.tar.gz')).toBe('gz');
    });

    it('should return empty string for files without extension', () => {
      expect(getFileExtension('filename')).toBe('');
      expect(getFileExtension('')).toBe('');
    });
  });

  describe('isValidImageType', () => {
    it('should validate image file types', () => {
      expect(isValidImageType('image.jpg')).toBe(true);
      expect(isValidImageType('photo.jpeg')).toBe(true);
      expect(isValidImageType('icon.png')).toBe(true);
      expect(isValidImageType('animation.gif')).toBe(true);
      expect(isValidImageType('modern.webp')).toBe(true);
    });

    it('should reject non-image file types', () => {
      expect(isValidImageType('document.pdf')).toBe(false);
      expect(isValidImageType('video.mp4')).toBe(false);
      expect(isValidImageType('audio.mp3')).toBe(false);
      expect(isValidImageType('text.txt')).toBe(false);
    });
  });

  describe('formatFileSize', () => {
    it('should format file sizes correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1048576)).toBe('1 MB');
      expect(formatFileSize(1073741824)).toBe('1 GB');
    });

    it('should handle decimal places', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(2621440)).toBe('2.5 MB');
    });
  });
});
