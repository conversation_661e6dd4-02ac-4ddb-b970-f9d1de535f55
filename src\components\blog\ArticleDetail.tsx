'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  CalendarIcon, 
  EyeIcon, 
  TagIcon, 
  ArrowLeftIcon,
  ShareIcon 
} from '@heroicons/react/24/outline';
import { Article } from '@/types';
import { formatDate, formatRelativeTime } from '@/lib/utils';

interface ArticleDetailProps {
  article: Article;
}

export default function ArticleDetail({ article }: ArticleDetailProps) {
  const [viewCount, setViewCount] = useState(article.view_count);

  useEffect(() => {
    // 模拟增加浏览量
    const incrementViewCount = async () => {
      try {
        // 这里后续会替换为真实的API调用
        setViewCount(prev => prev + 1);
      } catch (error) {
        console.error('更新浏览量失败:', error);
      }
    };

    incrementViewCount();
  }, []);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article.title,
          text: article.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.error('分享失败:', error);
      }
    } else {
      // 复制链接到剪贴板
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('链接已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
      }
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      {/* 返回按钮 */}
      <div className="mb-8">
        <Link
          href="/blog"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          返回文章列表
        </Link>
      </div>

      {/* 文章头部 */}
      <header className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          {article.title}
        </h1>
        
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-6">
          <div className="flex items-center gap-1">
            <CalendarIcon className="h-4 w-4" />
            <span>发布于 {formatDate(article.published_at || article.created_at)}</span>
          </div>
          
          <div className="flex items-center gap-1">
            <EyeIcon className="h-4 w-4" />
            <span>{viewCount} 次阅读</span>
          </div>
          
          {article.updated_at !== article.created_at && (
            <div className="text-xs text-gray-400">
              更新于 {formatRelativeTime(article.updated_at)}
            </div>
          )}
          
          <button
            onClick={handleShare}
            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ShareIcon className="h-4 w-4" />
            分享
          </button>
        </div>

        {/* 标签 */}
        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-6">
            {article.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                <TagIcon className="h-3 w-3 mr-1" />
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* 摘要 */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-2">文章摘要</h2>
          <p className="text-gray-700 leading-relaxed">
            {article.excerpt}
          </p>
        </div>
      </header>

      {/* 文章内容 */}
      <article className="prose prose-lg max-w-none">
        <div 
          className="markdown-content"
          dangerouslySetInnerHTML={{ 
            __html: article.content.replace(/\n/g, '<br>') 
          }} 
        />
      </article>

      {/* 文章底部 */}
      <footer className="mt-12 pt-8 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            <p>如果这篇文章对你有帮助，欢迎分享给更多人！</p>
          </div>
          
          <button
            onClick={handleShare}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <ShareIcon className="h-4 w-4 mr-2" />
            分享文章
          </button>
        </div>
      </footer>
    </div>
  );
}
