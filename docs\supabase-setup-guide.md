# Supabase 设置指南

## 什么是Supabase？

Supabase是一个开源的后端服务平台，为我们的博客提供：
- 数据库存储（文章、用户、产品等）
- 用户认证（登录、注册）
- 实时数据同步
- 文件存储

## 快速设置步骤

### 1. 注册Supabase账户

1. 访问 [https://supabase.com](https://supabase.com)
2. 点击 "Start your project"
3. 使用GitHub登录或邮箱注册

### 2. 创建新项目

1. 点击 "New Project"
2. 填写信息：
   - **项目名称**: `wblx-blog`
   - **数据库密码**: 设置一个强密码（请记住！）
   - **地区**: 选择 `Southeast Asia (Singapore)`
3. 点击 "Create new project"
4. 等待2-3分钟创建完成

### 3. 获取API密钥

项目创建完成后：
1. 点击左侧 "Settings" → "API"
2. 复制以下信息：
   - **Project URL**: `https://xxxxx.supabase.co`
   - **anon public key**: 一长串字符

### 4. 配置项目

将获取的信息填入 `.env.local` 文件：

```env
NEXT_PUBLIC_SUPABASE_URL=你的Project URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的anon public key
SUPABASE_SERVICE_ROLE_KEY=你的service_role key
```

### 5. 初始化数据库

1. 在Supabase仪表板，点击 "SQL Editor"
2. 点击 "New query"
3. 复制 `database/init.sql` 文件的全部内容
4. 粘贴到编辑器中
5. 点击 "Run" 执行

## 验证设置

设置完成后：
1. 重启开发服务器：`npm run dev`
2. 访问 `http://localhost:3000/admin/login`
3. 使用账户登录：
   - 邮箱: `<EMAIL>`
   - 密码: `password123`

## 常见问题

### Q: 忘记数据库密码怎么办？
A: 可以在项目设置中重置密码。

### Q: 免费额度够用吗？
A: 对于个人博客完全够用：
- 500MB 数据库存储
- 50,000 月活用户
- 2GB 文件存储

### Q: 数据会丢失吗？
A: Supabase提供自动备份，数据很安全。

### Q: 可以导出数据吗？
A: 可以，支持SQL导出和CSV导出。

## 替代方案

如果不想使用Supabase，项目也支持：
1. **本地模拟模式** - 数据存储在浏览器中
2. **其他数据库** - MySQL、MongoDB等
3. **自建PostgreSQL** - 完全自主控制

## 需要帮助？

如果在设置过程中遇到问题：
1. 查看Supabase官方文档
2. 在项目GitHub提交Issue
3. 联系项目维护者

---

**提示**: 第一次设置可能需要10-15分钟，但之后就可以专注于内容创作了！
