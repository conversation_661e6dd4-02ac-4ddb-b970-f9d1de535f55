'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CalendarIcon, EyeIcon } from '@heroicons/react/24/outline';
import { Article } from '@/types';
import { formatDate } from '@/lib/utils';

// 模拟数据，后续会替换为真实API调用
const mockArticles: Article[] = [
  {
    id: '1',
    title: '如何构建现代化的个人博客网站',
    content: '在这篇文章中，我将分享如何使用Next.js、Tailwind CSS和Supabase构建一个功能完整的个人博客网站...',
    excerpt: '学习如何使用现代技术栈构建个人博客，包括前端设计、后端API和数据库设计。',
    slug: 'how-to-build-modern-personal-blog',
    featured_image: '/images/blog-1.jpg',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['Next.js', 'React', 'Tailwind CSS', 'Supabase'],
    view_count: 1250,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    published_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'TypeScript最佳实践指南',
    content: 'TypeScript已经成为现代JavaScript开发的标准，本文将介绍一些实用的最佳实践...',
    excerpt: '掌握TypeScript的核心概念和最佳实践，提升代码质量和开发效率。',
    slug: 'typescript-best-practices-guide',
    published: true,
    author_id: '1',
    category_id: '2',
    tags: ['TypeScript', 'JavaScript', '最佳实践'],
    view_count: 890,
    created_at: '2024-01-10T14:30:00Z',
    updated_at: '2024-01-10T14:30:00Z',
    published_at: '2024-01-10T14:30:00Z',
  },
  {
    id: '3',
    title: '前端性能优化实战经验',
    content: '性能优化是前端开发中的重要话题，本文将分享一些实用的优化技巧和工具...',
    excerpt: '从实战角度分享前端性能优化的方法和技巧，让你的网站更快更流畅。',
    slug: 'frontend-performance-optimization-practices',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['性能优化', '前端开发', 'Web性能'],
    view_count: 1100,
    created_at: '2024-01-05T09:15:00Z',
    updated_at: '2024-01-05T09:15:00Z',
    published_at: '2024-01-05T09:15:00Z',
  },
];

export default function LatestArticles() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟API调用
    const fetchArticles = async () => {
      try {
        // 这里后续会替换为真实的API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        setArticles(mockArticles);
      } catch (error) {
        console.error('获取文章失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, []);

  if (loading) {
    return (
      <section className="py-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">最新文章</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4 w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded mb-4 w-1/2"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-3 bg-gray-300 rounded w-20"></div>
                    <div className="h-3 bg-gray-300 rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-12">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">最新文章</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          探索最新的技术见解和实践经验
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {articles.map((article) => (
          <article
            key={article.id}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            {article.featured_image && (
              <div className="h-48 bg-gray-200 relative">
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            )}
            <div className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                <Link
                  href={`/blog/${article.slug}`}
                  className="hover:text-blue-600 transition-colors"
                >
                  {article.title}
                </Link>
              </h3>
              <p className="text-gray-600 mb-4 line-clamp-3">
                {article.excerpt}
              </p>
              
              {article.tags && article.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
              
              <div className="flex justify-between items-center text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <CalendarIcon className="h-4 w-4" />
                  <span>{formatDate(article.published_at || article.created_at)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <EyeIcon className="h-4 w-4" />
                  <span>{article.view_count}</span>
                </div>
              </div>
            </div>
          </article>
        ))}
      </div>

      <div className="text-center">
        <Link
          href="/blog"
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
        >
          查看更多文章
        </Link>
      </div>
    </section>
  );
}
