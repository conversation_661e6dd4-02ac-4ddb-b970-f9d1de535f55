import { create } from 'zustand';
import { User } from '@/types';
import { supabase } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  checkAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  loading: true,

  signIn: async (email: string, password: string) => {
    try {
      // 模拟认证 - 仅用于演示
      if (email === '<EMAIL>' && password === 'password123') {
        const mockUser = {
          id: '1',
          email: '<EMAIL>',
          name: '管理员',
          role: 'admin' as const,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        set({ user: mockUser });

        // 保存到localStorage以便刷新后保持登录状态
        localStorage.setItem('mockUser', JSON.stringify(mockUser));

        return {};
      } else {
        return { error: '邮箱或密码错误' };
      }
    } catch {
      return { error: '登录失败，请重试' };
    }
  },

  signOut: async () => {
    try {
      // 清除模拟用户数据
      localStorage.removeItem('mockUser');
      set({ user: null });
    } catch {
      console.error('登出失败');
    }
  },

  checkAuth: async () => {
    try {
      set({ loading: true });

      // 检查localStorage中的模拟用户数据
      const mockUserData = localStorage.getItem('mockUser');
      if (mockUserData) {
        const user = JSON.parse(mockUserData);
        set({ user });
      }
    } catch {
      console.error('检查认证状态失败');
    } finally {
      set({ loading: false });
    }
  },
}));
