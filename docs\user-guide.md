# 使用教程

本教程将指导您如何使用王不留行714的个人博客网站的各项功能。

## 目录

1. [前台功能使用](#前台功能使用)
2. [管理后台使用](#管理后台使用)
3. [内容创作指南](#内容创作指南)
4. [常见问题解答](#常见问题解答)

## 前台功能使用

### 首页浏览

首页展示了网站的核心内容：

- **Hero 区域**: 网站介绍和快速导航
- **公告栏**: 重要通知和公告信息
- **最新文章**: 最近发布的博客文章
- **特色产品**: 推荐的产品和工具

### 文章阅读

#### 文章列表页面 (`/blog`)

1. **浏览文章**
   - 查看所有已发布的文章
   - 按分类筛选文章
   - 查看文章摘要、标签和发布时间

2. **分类筛选**
   - 点击分类标签筛选特定类型的文章
   - 支持的分类：前端开发、后端开发、TypeScript、生活感悟等

#### 文章详情页面 (`/blog/[slug]`)

1. **阅读文章**
   - 完整的文章内容展示
   - 支持 Markdown 格式渲染
   - 自动更新浏览量

2. **文章信息**
   - 发布时间和更新时间
   - 文章标签
   - 浏览次数统计

3. **分享功能**
   - 点击分享按钮分享文章
   - 支持原生分享 API（移动端）
   - 复制链接到剪贴板

### 产品展示

#### 产品列表页面 (`/products`)

1. **浏览产品**
   - 查看所有产品信息
   - 按状态筛选产品（已发布、即将发布、已停用）

2. **产品状态**
   - **已发布**: 可以直接访问的产品
   - **即将发布**: 正在开发中的产品
   - **已停用**: 不再维护的产品

3. **产品访问**
   - 点击"访问产品"按钮跳转到产品页面
   - 外部链接会在新标签页打开

### 关于页面 (`/about`)

了解作者的个人信息、技能特长、工作经历和联系方式。

## 管理后台使用

### 登录管理后台

1. **访问登录页面**
   - 地址：`/admin/login`
   - 使用管理员邮箱和密码登录

2. **测试账户**
   - 邮箱：`<EMAIL>`
   - 密码：`password123`

### 仪表板概览

登录后进入仪表板 (`/admin`)，可以查看：

- **统计数据**: 文章数量、浏览量、用户数等
- **最近活动**: 最新的操作记录
- **快速操作**: 常用功能的快捷入口

### 文章管理

#### 文章列表

1. **查看文章**
   - 显示所有文章（包括草稿）
   - 支持按状态筛选
   - 显示文章基本信息

2. **文章操作**
   - 编辑文章
   - 发布/取消发布
   - 删除文章

#### 创建/编辑文章

1. **基本信息**
   - 文章标题
   - URL 别名（slug）
   - 文章分类
   - 标签设置

2. **内容编辑**
   - 支持富文本编辑器
   - Markdown 语法支持
   - 实时预览功能

3. **发布设置**
   - 草稿/发布状态
   - 发布时间设置
   - 特色图片上传

### 公告管理

#### 创建公告

1. **公告类型**
   - 信息（蓝色）
   - 警告（黄色）
   - 成功（绿色）
   - 错误（红色）

2. **公告设置**
   - 标题和内容
   - 优先级设置
   - 过期时间（可选）
   - 发布状态

#### 管理公告

- 查看所有公告
- 编辑公告内容
- 设置公告优先级
- 删除过期公告

### 产品管理

#### 添加产品

1. **产品信息**
   - 产品名称
   - 详细描述
   - 产品链接

2. **状态设置**
   - 即将发布
   - 已发布
   - 已停用

3. **展示设置**
   - 是否为特色产品
   - 发布/预计发布时间
   - 产品图片

#### 管理产品

- 更新产品状态
- 修改产品信息
- 设置特色产品
- 删除产品

### 用户管理

- 查看用户列表
- 管理用户权限
- 设置管理员角色

## 内容创作指南

### 文章写作最佳实践

#### 1. 标题优化

- 使用清晰、描述性的标题
- 包含关键词，有利于 SEO
- 长度控制在 50-60 字符内

#### 2. 内容结构

```markdown
# 文章标题

## 引言
简要介绍文章主题和要解决的问题。

## 主要内容
### 子标题 1
具体内容...

### 子标题 2
具体内容...

## 总结
总结要点，提供行动建议。
```

#### 3. 标签使用

- 每篇文章建议 3-5 个标签
- 使用相关的技术标签
- 包含主题分类标签

#### 4. 摘要编写

- 控制在 150-200 字符
- 概括文章核心内容
- 吸引读者点击阅读

### Markdown 语法指南

#### 基础语法

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
~~删除线~~

- 无序列表项 1
- 无序列表项 2

1. 有序列表项 1
2. 有序列表项 2

[链接文本](https://example.com)

![图片描述](image-url.jpg)
```

#### 代码块

```markdown
行内代码：`console.log('Hello World')`

代码块：
```javascript
function greet(name) {
  console.log(`Hello, ${name}!`);
}
```

#### 表格

```markdown
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |
```

### 图片管理

#### 图片上传

1. **支持格式**: JPG, PNG, GIF, WebP
2. **大小限制**: 建议不超过 2MB
3. **尺寸建议**: 
   - 特色图片：1200x630px
   - 内容图片：800px 宽度

#### 图片优化

- 使用适当的压缩比例
- 为图片添加 alt 文本
- 考虑使用 WebP 格式

### SEO 优化

#### 1. URL 优化

- 使用有意义的 slug
- 包含关键词
- 使用连字符分隔单词

#### 2. 元数据设置

- 设置合适的页面标题
- 编写吸引人的描述
- 使用相关标签

#### 3. 内容优化

- 使用标题层级结构
- 包含相关关键词
- 提供有价值的内容

## 常见问题解答

### Q: 如何重置管理员密码？

A: 目前需要通过 Supabase 控制台重置密码：
1. 登录 Supabase 控制台
2. 进入 Authentication 页面
3. 找到管理员用户
4. 点击重置密码

### Q: 文章不显示怎么办？

A: 检查以下几点：
1. 文章是否已发布（published = true）
2. 发布时间是否正确
3. 数据库连接是否正常

### Q: 如何修改网站标题？

A: 修改 `src/app/layout.tsx` 文件中的 metadata 配置。

### Q: 如何添加新的文章分类？

A: 在管理后台的分类管理中添加，或直接在数据库的 categories 表中插入新记录。

### Q: 图片上传失败怎么办？

A: 检查：
1. 图片格式是否支持
2. 文件大小是否超限
3. Supabase 存储配置是否正确

### Q: 如何备份网站数据？

A: 
1. 在 Supabase 控制台导出数据库
2. 备份上传的图片文件
3. 保存环境变量配置

### Q: 网站加载慢怎么优化？

A: 
1. 优化图片大小和格式
2. 启用缓存策略
3. 使用 CDN 加速
4. 检查数据库查询性能

### Q: 如何自定义主题样式？

A: 修改 Tailwind CSS 配置文件 `tailwind.config.js`，或在组件中使用自定义 CSS 类。

### Q: 如何添加评论功能？

A: 可以集成第三方评论系统如：
- Disqus
- Gitalk
- Utterances

### Q: 如何设置邮件通知？

A: 在 Supabase 控制台配置邮件模板和 SMTP 设置。

---

如果您遇到其他问题，请查看项目的 GitHub Issues 或联系开发者。
