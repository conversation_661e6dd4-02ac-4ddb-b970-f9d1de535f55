'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  PencilIcon, 
  TrashIcon, 
  EyeIcon,
  CalendarIcon,
  TagIcon 
} from '@heroicons/react/24/outline';
import { Article } from '@/types';
import { formatDate, formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

// 模拟数据 - 后续会替换为真实API调用
const mockArticles: Article[] = [
  {
    id: '1',
    title: '如何构建现代化的个人博客网站',
    content: '在这篇文章中，我将分享如何使用Next.js、Tailwind CSS和Supabase构建一个功能完整的个人博客网站...',
    excerpt: '学习如何使用现代技术栈构建个人博客，包括前端设计、后端API和数据库设计。',
    slug: 'how-to-build-modern-personal-blog',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['Next.js', 'React', 'Tailwind CSS', 'Supabase'],
    view_count: 1250,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    published_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'TypeScript最佳实践指南',
    content: 'TypeScript已经成为现代JavaScript开发的标准，本文将介绍一些实用的最佳实践...',
    excerpt: '掌握TypeScript的核心概念和最佳实践，提升代码质量和开发效率。',
    slug: 'typescript-best-practices-guide',
    published: true,
    author_id: '1',
    category_id: '2',
    tags: ['TypeScript', 'JavaScript', '最佳实践'],
    view_count: 890,
    created_at: '2024-01-10T14:30:00Z',
    updated_at: '2024-01-10T14:30:00Z',
    published_at: '2024-01-10T14:30:00Z',
  },
  {
    id: '3',
    title: '前端性能优化实战经验（草稿）',
    content: '性能优化是前端开发中的重要话题，本文将分享一些实用的优化技巧和工具...',
    excerpt: '从实战角度分享前端性能优化的方法和技巧，让你的网站更快更流畅。',
    slug: 'frontend-performance-optimization-practices',
    published: false,
    author_id: '1',
    category_id: '1',
    tags: ['性能优化', '前端开发', 'Web性能'],
    view_count: 0,
    created_at: '2024-01-05T09:15:00Z',
    updated_at: '2024-01-20T16:30:00Z',
  },
];

export default function ArticleList() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'published' | 'draft'>('all');

  useEffect(() => {
    // 模拟API调用
    const fetchArticles = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setArticles(mockArticles);
      } catch (error) {
        console.error('获取文章失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, []);

  const handleDelete = async (id: string) => {
    if (!confirm('确定要删除这篇文章吗？此操作不可恢复。')) {
      return;
    }

    try {
      // 这里后续会替换为真实的API调用
      setArticles(articles.filter(article => article.id !== id));
      alert('文章已删除');
    } catch (error) {
      console.error('删除文章失败:', error);
      alert('删除失败，请重试');
    }
  };

  const handleTogglePublish = async (id: string, published: boolean) => {
    try {
      // 这里后续会替换为真实的API调用
      setArticles(articles.map(article => 
        article.id === id 
          ? { 
              ...article, 
              published: !published,
              published_at: !published ? new Date().toISOString() : null
            }
          : article
      ));
      alert(published ? '文章已设为草稿' : '文章已发布');
    } catch (error) {
      console.error('更新文章状态失败:', error);
      alert('操作失败，请重试');
    }
  };

  const filteredArticles = articles.filter(article => {
    if (filter === 'published') return article.published;
    if (filter === 'draft') return !article.published;
    return true;
  });

  if (loading) {
    return (
      <div className="space-y-4">
        {/* 筛选器骨架屏 */}
        <div className="flex gap-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-10 bg-gray-300 rounded-lg w-20 animate-pulse"></div>
          ))}
        </div>
        
        {/* 文章列表骨架屏 */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-6 border-b border-gray-200 animate-pulse">
              <div className="h-6 bg-gray-300 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 筛选器 */}
      <div className="flex gap-2">
        <button
          onClick={() => setFilter('all')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          全部 ({articles.length})
        </button>
        <button
          onClick={() => setFilter('published')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'published'
              ? 'bg-green-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          已发布 ({articles.filter(a => a.published).length})
        </button>
        <button
          onClick={() => setFilter('draft')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'draft'
              ? 'bg-yellow-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          草稿 ({articles.filter(a => !a.published).length})
        </button>
      </div>

      {/* 文章列表 */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {filteredArticles.length === 0 ? (
          <div className="p-12 text-center">
            <p className="text-gray-500">暂无文章</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredArticles.map((article) => (
              <div key={article.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {article.title}
                      </h3>
                      <span className={cn(
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        article.published
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      )}>
                        {article.published ? '已发布' : '草稿'}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {article.excerpt}
                    </p>
                    
                    {/* 标签 */}
                    {article.tags && article.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {article.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            <TagIcon className="h-3 w-3 mr-1" />
                            {tag}
                          </span>
                        ))}
                        {article.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{article.tags.length - 3} 更多
                          </span>
                        )}
                      </div>
                    )}
                    
                    {/* 元信息 */}
                    <div className="flex items-center text-xs text-gray-500 space-x-4">
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-3 w-3" />
                        <span>
                          {article.published_at 
                            ? `发布于 ${formatDate(article.published_at)}`
                            : `创建于 ${formatDate(article.created_at)}`
                          }
                        </span>
                      </div>
                      
                      {article.updated_at !== article.created_at && (
                        <span>更新于 {formatRelativeTime(article.updated_at)}</span>
                      )}
                      
                      <div className="flex items-center gap-1">
                        <EyeIcon className="h-3 w-3" />
                        <span>{article.view_count} 次浏览</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2 ml-4">
                    <Link
                      href={`/admin/articles/${article.id}/edit`}
                      className="inline-flex items-center p-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                      title="编辑文章"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </Link>
                    
                    <button
                      onClick={() => handleTogglePublish(article.id, article.published)}
                      className={cn(
                        'inline-flex items-center px-3 py-2 border border-transparent rounded-md text-sm font-medium transition-colors',
                        article.published
                          ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                          : 'text-green-700 bg-green-100 hover:bg-green-200'
                      )}
                    >
                      {article.published ? '设为草稿' : '发布'}
                    </button>
                    
                    <button
                      onClick={() => handleDelete(article.id)}
                      className="inline-flex items-center p-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 transition-colors"
                      title="删除文章"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
