module.exports = {
  apps: [
    {
      name: 'wblx-blog',
      script: 'node_modules/next/dist/bin/next',
      args: 'start',
      cwd: '/www/wwwroot/www.wblx.xyz',
      instances: 1, // 根据服务器配置调整
      exec_mode: 'fork', // 小服务器建议用fork模式
      watch: false,
      max_memory_restart: '512M', // 根据服务器内存调整
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      log_date_format: 'YYYY-MM-DD HH:mm Z',
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
    },
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/personal-blog.git',
      path: '/var/www/personal-blog',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
    },
  },
};
