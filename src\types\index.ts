// 用户类型
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

// 文章类型
export interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  featured_image?: string;
  published: boolean;
  author_id: string;
  category_id?: string;
  tags?: string[];
  view_count: number;
  created_at: string;
  updated_at: string;
  published_at?: string | null;
  author?: User;
  category?: Category;
}

// 分类类型
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  created_at: string;
  updated_at: string;
}

// 公告类型
export interface Announcement {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  published: boolean;
  priority: number;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

// 产品类型
export interface Product {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  product_url?: string;
  status: 'coming_soon' | 'available' | 'discontinued';
  featured: boolean;
  launch_date?: string;
  created_at: string;
  updated_at: string;
}

// API响应类型
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 表单类型
export interface ArticleFormData {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  featured_image?: string;
  published: boolean;
  category_id?: string;
  tags?: string[];
}

export interface AnnouncementFormData {
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  published: boolean;
  priority: number;
  expires_at?: string;
}

export interface ProductFormData {
  name: string;
  description: string;
  image_url?: string;
  product_url?: string;
  status: 'coming_soon' | 'available' | 'discontinued';
  featured: boolean;
  launch_date?: string;
}
