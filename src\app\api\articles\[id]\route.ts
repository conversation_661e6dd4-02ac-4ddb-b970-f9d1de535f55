import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

interface RouteParams {
  params: Promise<{ id: string }>;
}

// GET /api/articles/[id] - 获取单个文章
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        category:categories(*),
        author:users(id, name, email)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: '文章不存在' },
          { status: 404 }
        );
      }
      console.error('获取文章失败:', error);
      return NextResponse.json(
        { error: '获取文章失败' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PUT /api/articles/[id] - 更新文章
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;
    const body = await request.json();

    // 验证必填字段
    if (!body.title || !body.content || !body.slug) {
      return NextResponse.json(
        { error: '标题、内容和URL别名为必填项' },
        { status: 400 }
      );
    }

    // 检查slug是否已被其他文章使用
    const { data: existingArticle } = await supabase
      .from('articles')
      .select('id')
      .eq('slug', body.slug)
      .neq('id', id)
      .single();

    if (existingArticle) {
      return NextResponse.json(
        { error: 'URL别名已被其他文章使用' },
        { status: 400 }
      );
    }

    const updateData: Record<string, unknown> = {
      title: body.title,
      content: body.content,
      excerpt: body.excerpt || '',
      slug: body.slug,
      featured_image: body.featured_image,
      published: body.published || false,
      category_id: body.category_id,
      tags: body.tags || [],
    };

    // 如果从未发布变为发布，设置发布时间
    if (body.published && !body.published_at) {
      updateData.published_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('articles')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        category:categories(*),
        author:users(id, name, email)
      `)
      .single();

    if (error) {
      console.error('更新文章失败:', error);
      return NextResponse.json(
        { error: '更新文章失败' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// DELETE /api/articles/[id] - 删除文章
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    const { error } = await supabase
      .from('articles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('删除文章失败:', error);
      return NextResponse.json(
        { error: '删除文章失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: '文章已删除' });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}

// PATCH /api/articles/[id]/view - 增加浏览量
export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    // 先获取当前浏览量
    const { data: currentData } = await supabase
      .from('articles')
      .select('view_count')
      .eq('id', id)
      .single();

    const newViewCount = (currentData?.view_count || 0) + 1;

    const { data, error } = await supabase
      .from('articles')
      .update({ view_count: newViewCount })
      .eq('id', id)
      .select('view_count')
      .single();

    if (error) {
      console.error('更新浏览量失败:', error);
      return NextResponse.json(
        { error: '更新浏览量失败' },
        { status: 500 }
      );
    }

    return NextResponse.json({ view_count: data.view_count });
  } catch (error) {
    console.error('API错误:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
