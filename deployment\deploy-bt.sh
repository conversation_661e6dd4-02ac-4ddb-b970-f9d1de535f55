#!/bin/bash

# 宝塔面板部署脚本
# 使用方法: chmod +x deploy-bt.sh && ./deploy-bt.sh

set -e

# 配置变量
PROJECT_DIR="/www/wwwroot/www.wblx.xyz"
APP_NAME="wblx-blog"
BACKUP_DIR="/www/backup/wblx-blog"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在正确目录
if [ ! -f "package.json" ]; then
    log_error "请在项目根目录执行此脚本"
    exit 1
fi

log_info "开始部署到宝塔面板..."

# 1. 备份当前版本
if [ -d "$PROJECT_DIR" ]; then
    log_info "备份当前版本..."
    mkdir -p "$BACKUP_DIR"
    cp -r "$PROJECT_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
fi

# 2. 停止应用
log_info "停止当前应用..."
pm2 stop $APP_NAME || true

# 3. 安装依赖
log_info "安装依赖..."
npm ci --only=production

# 4. 构建项目
log_info "构建项目..."
npm run build

# 5. 检查环境变量
if [ ! -f ".env.local" ]; then
    log_warn "未找到 .env.local 文件，请确保已配置环境变量"
fi

# 6. 启动应用
log_info "启动应用..."
pm2 start ecosystem.config.js --env production

# 7. 重新加载PM2
pm2 reload $APP_NAME

# 8. 保存PM2配置
pm2 save

# 9. 健康检查
log_info "执行健康检查..."
sleep 10

if curl -f http://localhost:3000 > /dev/null 2>&1; then
    log_info "✅ 部署成功！应用正在运行"
    log_info "🌐 访问地址: https://www.wblx.xyz"
    log_info "🔧 管理后台: https://www.wblx.xyz/admin/login"
else
    log_error "❌ 健康检查失败，请检查应用状态"
    pm2 logs $APP_NAME --lines 20
    exit 1
fi

log_info "部署完成！"
