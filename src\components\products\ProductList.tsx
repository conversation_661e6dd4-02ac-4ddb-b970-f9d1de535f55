'use client';

import { useState, useEffect } from 'react';
import { 
  ArrowTopRightOnSquareIcon, 
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';
import { Product } from '@/types';
import { formatDate } from '@/lib/utils';
import { cn } from '@/lib/utils';

// 模拟数据
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'AI写作助手',
    description: '基于人工智能的智能写作工具，帮助您快速生成高质量的文章内容。支持多种写作风格和主题，内置语法检查和优化建议功能。',
    image_url: '/images/product-1.jpg',
    product_url: 'https://example.com/ai-writer',
    status: 'available',
    featured: true,
    launch_date: '2024-01-15T00:00:00Z',
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: '代码片段管理器',
    description: '专为开发者设计的代码片段管理工具，支持多种编程语言，云端同步，团队协作。提供智能搜索和标签分类功能，让您的代码片段井井有条。',
    image_url: '/images/product-2.jpg',
    status: 'coming_soon',
    featured: true,
    launch_date: '2024-02-20T00:00:00Z',
    created_at: '2024-01-12T14:30:00Z',
    updated_at: '2024-01-12T14:30:00Z',
  },
  {
    id: '3',
    name: '个人知识库',
    description: '构建您的个人知识管理系统，支持笔记整理、标签分类、全文搜索和知识图谱可视化。帮助您更好地组织和利用个人知识资产。',
    image_url: '/images/product-3.jpg',
    status: 'coming_soon',
    featured: true,
    launch_date: '2024-03-10T00:00:00Z',
    created_at: '2024-01-08T09:15:00Z',
    updated_at: '2024-01-08T09:15:00Z',
  },
  {
    id: '4',
    name: '任务管理工具',
    description: '简洁高效的任务管理工具，支持项目分组、优先级设置、进度跟踪和团队协作。采用看板式界面，让任务管理变得更加直观。',
    image_url: '/images/product-4.jpg',
    status: 'coming_soon',
    featured: false,
    launch_date: '2024-04-15T00:00:00Z',
    created_at: '2024-01-05T16:45:00Z',
    updated_at: '2024-01-05T16:45:00Z',
  },
  {
    id: '5',
    name: '数据可视化平台',
    description: '强大的数据可视化平台，支持多种图表类型，拖拽式操作，实时数据更新。帮助您将复杂的数据转化为直观的图表和报告。',
    image_url: '/images/product-5.jpg',
    status: 'discontinued',
    featured: false,
    launch_date: '2023-10-01T00:00:00Z',
    created_at: '2023-09-15T12:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const getStatusInfo = (status: Product['status']) => {
  switch (status) {
    case 'available':
      return {
        label: '已发布',
        icon: CheckCircleIcon,
        className: 'bg-green-100 text-green-800',
        iconClassName: 'text-green-600',
      };
    case 'coming_soon':
      return {
        label: '即将发布',
        icon: ClockIcon,
        className: 'bg-yellow-100 text-yellow-800',
        iconClassName: 'text-yellow-600',
      };
    case 'discontinued':
      return {
        label: '已停用',
        icon: XCircleIcon,
        className: 'bg-gray-100 text-gray-800',
        iconClassName: 'text-gray-600',
      };
    default:
      return {
        label: '未知',
        icon: ClockIcon,
        className: 'bg-gray-100 text-gray-800',
        iconClassName: 'text-gray-600',
      };
  }
};

export default function ProductList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [filter, setFilter] = useState<'all' | 'available' | 'coming_soon' | 'discontinued'>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟API调用
    const fetchProducts = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setProducts(mockProducts);
      } catch (error) {
        console.error('获取产品失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  const filteredProducts = filter === 'all' 
    ? products 
    : products.filter(product => product.status === filter);

  if (loading) {
    return (
      <div className="space-y-8">
        {/* 筛选器骨架屏 */}
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-10 bg-gray-300 rounded-lg w-24 animate-pulse"></div>
          ))}
        </div>
        
        {/* 产品列表骨架屏 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
              <div className="h-48 bg-gray-300"></div>
              <div className="p-6">
                <div className="h-6 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded mb-4"></div>
                <div className="flex justify-between items-center">
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                  <div className="h-10 bg-gray-300 rounded w-24"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 状态筛选器 */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setFilter('all')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          全部产品
        </button>
        <button
          onClick={() => setFilter('available')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'available'
              ? 'bg-green-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          已发布
        </button>
        <button
          onClick={() => setFilter('coming_soon')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'coming_soon'
              ? 'bg-yellow-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          即将发布
        </button>
        <button
          onClick={() => setFilter('discontinued')}
          className={cn(
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            filter === 'discontinued'
              ? 'bg-gray-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          )}
        >
          已停用
        </button>
      </div>

      {/* 产品网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {filteredProducts.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500">暂无产品</p>
          </div>
        ) : (
          filteredProducts.map((product) => {
            const statusInfo = getStatusInfo(product.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <div
                key={product.id}
                className={cn(
                  'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow',
                  product.featured && 'ring-2 ring-blue-500 ring-opacity-50'
                )}
              >
                {/* 产品图片/占位符 */}
                <div className="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="absolute top-4 right-4">
                    <span className={cn(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      statusInfo.className
                    )}>
                      <StatusIcon className={cn('h-3 w-3 mr-1', statusInfo.iconClassName)} />
                      {statusInfo.label}
                    </span>
                  </div>
                  {product.featured && (
                    <div className="absolute top-4 left-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-600 text-white">
                        特色产品
                      </span>
                    </div>
                  )}
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {product.name}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {product.description}
                  </p>
                  
                  {product.launch_date && (
                    <div className="text-sm text-gray-500 mb-4">
                      {product.status === 'available' ? '发布时间' : '预计发布'}: {formatDate(product.launch_date)}
                    </div>
                  )}
                  
                  <div className="flex justify-between items-center">
                    <span className={cn(
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      statusInfo.className
                    )}>
                      <StatusIcon className={cn('h-3 w-3 mr-1', statusInfo.iconClassName)} />
                      {statusInfo.label}
                    </span>
                    
                    {product.status === 'available' && product.product_url ? (
                      <a
                        href={product.product_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                      >
                        访问产品
                        <ArrowTopRightOnSquareIcon className="ml-2 h-4 w-4" />
                      </a>
                    ) : (
                      <button
                        disabled
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100 cursor-not-allowed"
                      >
                        {product.status === 'coming_soon' ? '敬请期待' : '已停用'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
}
