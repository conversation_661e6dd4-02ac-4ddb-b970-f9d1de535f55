import Layout from '@/components/layout/Layout';
import { 
  CodeBracketIcon, 
  AcademicCapIcon, 
  HeartIcon,
  RocketLaunchIcon 
} from '@heroicons/react/24/outline';

export const metadata = {
  title: '关于我 - 王不留行714的个人博客',
  description: '了解更多关于王不留行714的信息',
};

const skills = [
  'JavaScript/TypeScript',
  'React/Next.js',
  'Vue.js/Nuxt.js',
  'Node.js',
  'Python',
  'PostgreSQL/MySQL',
  'Docker',
  'AWS/云服务',
];

const experiences = [
  {
    title: '全栈开发工程师',
    company: '某科技公司',
    period: '2022 - 至今',
    description: '负责前端和后端开发，参与多个大型项目的架构设计和开发工作。',
  },
  {
    title: '前端开发工程师',
    company: '某互联网公司',
    period: '2020 - 2022',
    description: '专注于前端开发，使用React和Vue.js构建用户界面，优化用户体验。',
  },
  {
    title: '软件开发实习生',
    company: '某软件公司',
    period: '2019 - 2020',
    description: '参与Web应用开发，学习软件开发流程和团队协作。',
  },
];

export default function AboutPage() {
  return (
    <Layout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 个人介绍 */}
        <section className="text-center mb-16">
          <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-8 flex items-center justify-center">
            <span className="text-4xl font-bold text-white">王</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">王不留行714</h1>
          <p className="text-xl text-gray-600 mb-6">
            全栈开发工程师 · 技术爱好者 · 终身学习者
          </p>
          <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
            你好！我是王不留行714，一名热爱技术的全栈开发工程师。
            我专注于现代Web开发技术，喜欢探索新的技术栈，并通过这个博客分享我的学习心得和实践经验。
            除了技术，我也关注产品设计和用户体验，致力于创造有价值的数字产品。
          </p>
        </section>

        {/* 技能特长 */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <CodeBracketIcon className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">技能特长</h2>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {skills.map((skill) => (
              <div
                key={skill}
                className="bg-white rounded-lg shadow-md p-4 text-center hover:shadow-lg transition-shadow"
              >
                <span className="text-sm font-medium text-gray-900">{skill}</span>
              </div>
            ))}
          </div>
        </section>

        {/* 工作经历 */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <AcademicCapIcon className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">工作经历</h2>
          </div>
          <div className="space-y-6">
            {experiences.map((exp, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">{exp.title}</h3>
                  <span className="text-sm text-gray-500">{exp.period}</span>
                </div>
                <p className="text-blue-600 font-medium mb-2">{exp.company}</p>
                <p className="text-gray-700">{exp.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* 兴趣爱好 */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <HeartIcon className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">兴趣爱好</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">技术探索</h3>
              <p className="text-gray-700">
                热衷于学习新的编程语言和框架，关注前沿技术发展，
                喜欢通过实际项目来验证和应用新技术。
              </p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">开源贡献</h3>
              <p className="text-gray-700">
                积极参与开源项目，贡献代码和文档，
                相信开源精神能够推动技术进步和知识共享。
              </p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">写作分享</h3>
              <p className="text-gray-700">
                通过博客文章分享技术心得和学习经验，
                希望能够帮助更多的开发者成长。
              </p>
            </div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">产品思维</h3>
              <p className="text-gray-700">
                关注用户体验和产品设计，
                致力于创造既有技术深度又有实用价值的产品。
              </p>
            </div>
          </div>
        </section>

        {/* 联系方式 */}
        <section className="text-center">
          <div className="flex items-center justify-center mb-8">
            <RocketLaunchIcon className="h-8 w-8 text-blue-600 mr-3" />
            <h2 className="text-3xl font-bold text-gray-900">联系我</h2>
          </div>
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
            <p className="text-lg text-gray-700 mb-6">
              如果你对我的文章或项目感兴趣，或者想要交流技术话题，
              欢迎通过以下方式联系我：
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                发送邮件
              </a>
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                GitHub
              </a>
            </div>
          </div>
        </section>
      </div>
    </Layout>
  );
}
