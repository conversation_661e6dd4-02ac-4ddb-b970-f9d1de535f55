'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { CalendarIcon, EyeIcon, TagIcon } from '@heroicons/react/24/outline';
import { Article, Category } from '@/types';
import { formatDate } from '@/lib/utils';

// 模拟数据
const mockCategories: Category[] = [
  { id: '1', name: '前端开发', slug: 'frontend', description: '前端技术相关文章', created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: '2', name: 'TypeScript', slug: 'typescript', description: 'TypeScript相关文章', created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
  { id: '3', name: '生活感悟', slug: 'life', description: '生活感悟和思考', created_at: '2024-01-01T00:00:00Z', updated_at: '2024-01-01T00:00:00Z' },
];

const mockArticles: Article[] = [
  {
    id: '1',
    title: '如何构建现代化的个人博客网站',
    content: '在这篇文章中，我将分享如何使用Next.js、Tailwind CSS和Supabase构建一个功能完整的个人博客网站...',
    excerpt: '学习如何使用现代技术栈构建个人博客，包括前端设计、后端API和数据库设计。',
    slug: 'how-to-build-modern-personal-blog',
    featured_image: '/images/blog-1.jpg',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['Next.js', 'React', 'Tailwind CSS', 'Supabase'],
    view_count: 1250,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
    published_at: '2024-01-15T10:00:00Z',
    category: mockCategories[0],
  },
  {
    id: '2',
    title: 'TypeScript最佳实践指南',
    content: 'TypeScript已经成为现代JavaScript开发的标准，本文将介绍一些实用的最佳实践...',
    excerpt: '掌握TypeScript的核心概念和最佳实践，提升代码质量和开发效率。',
    slug: 'typescript-best-practices-guide',
    published: true,
    author_id: '1',
    category_id: '2',
    tags: ['TypeScript', 'JavaScript', '最佳实践'],
    view_count: 890,
    created_at: '2024-01-10T14:30:00Z',
    updated_at: '2024-01-10T14:30:00Z',
    published_at: '2024-01-10T14:30:00Z',
    category: mockCategories[1],
  },
  {
    id: '3',
    title: '前端性能优化实战经验',
    content: '性能优化是前端开发中的重要话题，本文将分享一些实用的优化技巧和工具...',
    excerpt: '从实战角度分享前端性能优化的方法和技巧，让你的网站更快更流畅。',
    slug: 'frontend-performance-optimization-practices',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['性能优化', '前端开发', 'Web性能'],
    view_count: 1100,
    created_at: '2024-01-05T09:15:00Z',
    updated_at: '2024-01-05T09:15:00Z',
    published_at: '2024-01-05T09:15:00Z',
    category: mockCategories[0],
  },
  {
    id: '4',
    title: '远程工作的思考与实践',
    content: '疫情改变了我们的工作方式，远程工作成为了新常态。本文分享我在远程工作中的一些思考和实践经验...',
    excerpt: '分享远程工作的经验和思考，如何在家中保持高效工作状态。',
    slug: 'remote-work-thoughts-and-practices',
    published: true,
    author_id: '1',
    category_id: '3',
    tags: ['远程工作', '生活方式', '效率'],
    view_count: 650,
    created_at: '2024-01-01T16:00:00Z',
    updated_at: '2024-01-01T16:00:00Z',
    published_at: '2024-01-01T16:00:00Z',
    category: mockCategories[2],
  },
  {
    id: '5',
    title: 'React Hooks深度解析',
    content: 'React Hooks彻底改变了我们编写React组件的方式，本文将深入解析各种Hooks的使用场景和最佳实践...',
    excerpt: '深入理解React Hooks的原理和使用方法，掌握现代React开发技巧。',
    slug: 'react-hooks-deep-dive',
    published: true,
    author_id: '1',
    category_id: '1',
    tags: ['React', 'Hooks', 'JavaScript'],
    view_count: 1350,
    created_at: '2023-12-28T11:20:00Z',
    updated_at: '2023-12-28T11:20:00Z',
    published_at: '2023-12-28T11:20:00Z',
    category: mockCategories[0],
  },
];

export default function BlogList() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 模拟API调用
    const fetchData = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setArticles(mockArticles);
        setCategories(mockCategories);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredArticles = selectedCategory === 'all' 
    ? articles 
    : articles.filter(article => article.category_id === selectedCategory);

  if (loading) {
    return (
      <div className="space-y-8">
        {/* 分类筛选骨架屏 */}
        <div className="flex flex-wrap gap-2">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-8 bg-gray-300 rounded-full w-20 animate-pulse"></div>
          ))}
        </div>
        
        {/* 文章列表骨架屏 */}
        <div className="space-y-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
              <div className="h-6 bg-gray-300 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-gray-300 rounded mb-2"></div>
              <div className="h-4 bg-gray-300 rounded mb-4 w-1/2"></div>
              <div className="flex justify-between items-center">
                <div className="h-4 bg-gray-300 rounded w-32"></div>
                <div className="h-4 bg-gray-300 rounded w-16"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* 分类筛选 */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => setSelectedCategory('all')}
          className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
            selectedCategory === 'all'
              ? 'bg-blue-600 text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          全部
        </button>
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.name}
          </button>
        ))}
      </div>

      {/* 文章列表 */}
      <div className="space-y-6">
        {filteredArticles.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">暂无文章</p>
          </div>
        ) : (
          filteredArticles.map((article) => (
            <article
              key={article.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    <Link
                      href={`/blog/${article.slug}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      {article.title}
                    </Link>
                  </h2>
                  
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {article.excerpt}
                  </p>
                  
                  {/* 标签 */}
                  {article.tags && article.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {article.tags.map((tag) => (
                        <span
                          key={tag}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          <TagIcon className="h-3 w-3 mr-1" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {/* 元信息 */}
                  <div className="flex items-center text-sm text-gray-500 space-x-4">
                    {article.category && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {article.category.name}
                      </span>
                    )}
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-4 w-4" />
                      <span>{formatDate(article.published_at || article.created_at)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <EyeIcon className="h-4 w-4" />
                      <span>{article.view_count}</span>
                    </div>
                  </div>
                </div>
              </div>
            </article>
          ))
        )}
      </div>
    </div>
  );
}
